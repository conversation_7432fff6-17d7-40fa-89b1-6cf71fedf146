*:focus,*:active{outline: none;}
 
.fcre-container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.5;
}

.fcre-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;  
    margin-right: -15px;
    margin-bottom: 20px; 
}

.fcre-col {
    flex: 1;
    padding: 0 15px;
    box-sizing: border-box;
    min-width: 0;
}

.fcre-col-12 { flex: 0 0 100%; max-width: 100%; } /* Full width */
.fcre-col-6 { flex: 0 0 50%; max-width: 50%; } /* Half width */
.fcre-col-4 { flex: 0 0 33.3333%; max-width: 33.3333%; } /* One-third */
.fcre-col-3 { flex: 0 0 25%; max-width: 25%; } /* One-fourth */
.fcre-col-2 { flex: 0 0 16.6666%; max-width: 16.6666%; } /* One-sixth */

/* Responsive Adjustments */
@media (max-width: 782px) {
    .fcre-col-12,
    .fcre-col-6,
    .fcre-col-4,
    .fcre-col-3,
    .fcre-col-2 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.fcre-form-group {
    padding: 12px;
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-bottom: 10px;
}

.fcre-form-group > input[type="text"],
.fcre-form-group > input[type="number"],
.fcre-form-group > select,
.fcre-form-group > textarea {
    width: 100%;
    padding: 8px;
    margin: 0;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.fcre-form-group > label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}
.fcre-input-group{
    display: flex;
    flex-wrap: nowrap;
    gap: 10px;
    align-items: center;
}
.fcre-input-group > input[type="text"],
.fcre-input-group > input[type="number"],
.fcre-input-group > select,
.fcre-input-group > textarea {
    width: 100%;
    padding: 8px;
    margin: 0;
    box-sizing: border-box;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.fcre-wysiwyg-wrapper > label{
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}
.fcre-container fieldset {
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
    background: #fdfdfd;
}
.fcre-container fieldset legend {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    padding: 0 5px;
}

/* .fcre-tab-sidebar {
    border: 1px solid #ddd;
    width: 300px;
    float: left;
    position: relative;
    min-height: 90vh;
    background: #ffffff;
}
.fcre-tab-content {
    float: left;
    width: 900px;
    border: 1px solid #ddd;
    padding: 0 10px;
    min-height: 90vh;
    background: #ffffff;
} */

/* .tab-content-header {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.tab-content-header h2 {
    margin-top: 0;
    font-size: 24px;
    font-weight: bold;
} */

.tab-content-header input[type="submit"] {
    margin-top: 10px;
}

.fcre-settings-card {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    margin-bottom: 20px;
}
.fcre-settings-card .card-title{margin-top:0;}
.fcre-types-table {
    width: 100%;
    border-collapse: collapse;
}

.fcre-types-table th, .fcre-types-table td {
    padding: 12px 0;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.fcre-types-table tbody tr:last-child td {
    border-bottom: none;
}

.fcre-types-table input[type="text"] {
    width: 90%;
    padding: 8px;
    font-size: 14px;
}

.drag-handler {
    cursor: move;
}
.move-handle {
    display: inline-block;
    width: 14px;
    height: 30px;
    margin-right: 10px;
    position: relative;
    margin-left: 10px;
}

.move-handle::before,
.move-handle::after {
    content: '';
    display: block;
    width: 5px; /* Size of each dot */
    height: 5px;
    background: #999;
    border-radius: 50%;
    position: absolute;
}

/* Left column: 3 dots using ::before and box-shadow */
.move-handle::before {
    top: 4px;
    left: 2px;
    box-shadow: 0 10px 0 #999, 0 20px 0 #999; /* Creates 2 more dots below */
}

/* Right column: 3 dots using ::after and box-shadow */
.move-handle::after {
    top: 4px;
    left: 10px;
    box-shadow: 0 10px 0 #999, 0 20px 0 #999; /* Creates 2 more dots below */
}

.row-remove span {
    cursor: pointer;
}
.row-remove span:hover {
    color: #c9302c;
}

/* .fcre-btn-add-more {
    display: inline-block;
    margin-top: 10px;
    padding: 8px 15px;
    background-color: #0073aa;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
} */

.fcre-btn-add-more:hover {
    background-color: #005177;
}

/* Placeholder for drag sorting */
.ui-state-highlight {
    background: #f0f0f0;
    height: 50px;
    border: 2px dashed #ccc;
    margin-bottom: 10px;
}

/* Field sorting specific placeholder */
.fcre-sortable-placeholder {
    background: #f9f9f9;
    border: 2px dashed #0073aa;
    height: 50px;
    margin-bottom: 1px;
    border-radius: 4px;
}


.fcre-sort-table tr{
    position:relative;
}
.row-remove{cursor:pointer;position:relative;width:30px;}
.row-remove:hover{background:#f5f5f5;}
.icon-trash{width:15px;height:27px;background:url(../img/trash.svg) center center no-repeat;background-size:100%;display:inline-block;position:absolute;top:7px;right:9px;cursor:pointer;}
.fcre-hide{display:none;}
.fcre-show{display:block;}

.fcre-multiselect-wrapper{width:100%;position:relative;}
.fcre-multiselect-wrapper .fcre-filter-label{font-weight:600;}
.fcre-multiselect-wrapper .filter-select{position:relative;border:1px solid #a7a7a7;width:100%;background:#ffffff;padding:12px 0;margin-top:5px;border-radius:4px;}
.fcre-multiselect-wrapper .filter-select-arrow{position:absolute;top:17px;right:5px;border-top:6px solid #a7a7a7;border-left:6px solid transparent;border-bottom:6px solid transparent;border-right:6px solid transparent;z-index:1;}
.fcre-multiselect-wrapper .filter-onclick{position:absolute;top:0;left:0;width:100%;background:transparent;cursor:pointer;z-index:1;height:40px;}
.fcre-multiselect-wrapper .filter-dropdown{background:#ffffff;position:absolute;width:100%;top:41px;left:0;z-index:2;border:1px solid #a7a7a7;}
.fcre-multiselect-wrapper .filter-dropdown-area{padding-left:10px;padding-right:10px;padding-top:10px;}
.fcre-multiselect-wrapper .filter-placeholder{font-size:12px;display:block;width:100%;padding:0 10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}
.fcre-multiselect-wrapper ul.fcre-dropdown{margin-top:5px;}
.fcre-multiselect-wrapper .fcre-dropdown li{list-style:none;}

.fcre-tab-titles { list-style: none; margin: 0; padding: 0; display: flex; }
.fcre-tab-titles li { padding: 10px 20px; background: #f1f1f1; cursor: pointer; margin-right: 5px;border: 1px solid #c3c4c7;border-bottom: none;margin-bottom: 0;font-weight: 600; }
.fcre-tab-titles li.active {background: #ffffff;}
.fcre-tab-content { background: #ffffff; padding: 20px; border: 1px solid #c3c4c7;}


.fcre-files-gallery-metabox-list {margin:0}
.fcre-files-gallery-metabox-list li{float:left;position:relative;width:100%;text-align:left;padding-left:5px;cursor:move;border:1px solid #ddd;}
.fcre-files-gallery-metabox-list li:hover{background:#f5f5f5;}
.fcre-files-gallery-metabox-list .icon-preview{width:40px;margin-right:10px;}
.fcre-files-gallery-metabox-list .file-remove{display:none;background:url("../img/close.png") center center no-repeat #fff;height:20px;width:20px;border-radius:100%;position:absolute;top:-4px;right:-10px;background-size:70%;box-shadow:0 0 4px 0 black;}
.fcre-files-gallery-metabox-list .file-edit{display:none;background:url("../img/edit.png") center center no-repeat #fff;height:20px;width:20px;border-radius:100%;position:absolute;top:-3px;right:13px;background-size:70%;box-shadow:0 0 4px 0 black;}
.fcre-files-gallery-metabox-list li:hover .file-edit{display:block!important;}
.fcre-files-gallery-metabox-list li:hover .file-remove{display:block!important;}
a.pdf,a.docx,a.doc,a.xlsx,a.xls,a.zip,a.jpg,a.jpeg,a.png,a.bmp,a.gif,a.pptx,a.ppt{display:block;width:fit-content;}
a.jpg,a.jpeg,a.png,a.bmp,a.gif{background:url('../img/image.png') center left no-repeat;padding:10px 0 10px 30px;font-size:16px;text-decoration:none;}
a.pdf{background:url('../img/pdf.png') 0 8px no-repeat;padding:10px 0 10px 30px;font-size:16px;text-decoration:none;}
a.docx{background:url('../img/word.png') center left no-repeat;padding:10px 0 10px 30px;font-size:16px;text-decoration:none;}
a.doc{background:url('../img/word.png') center left no-repeat;padding:10px 0 10px 30px;font-size:16px;text-decoration:none;}
a.pptx{background:url('../img/pptx.png') center left no-repeat;padding:10px 0 10px 30px;font-size:16px;text-decoration:none;}
a.xlsx{background:url('../img/excel.png') center left no-repeat;padding:10px 0 10px 30px;font-size:16px;text-decoration:none;}
a.zip{background:url('../img/zip.png') center left no-repeat;padding:10px 0 10px 30px;font-size:16px;text-decoration:none;}


.fcre-photo-gallery-metabox-list li{position:relative;display:inline-block;width:150px;text-align:center;margin:0 10px 10px 0;cursor:move;border-radius:5px;}
.fcre-photo-gallery-metabox-list li img{border-radius:5px;width:100%;height:100%;object-fit:cover;}
.fcre-photo-gallery-metabox-list li input{width:100%;border-radius:5px;}
.fcre-photo-gallery-metabox-list li .change-photo{display:none;background:url("../img/edit.png") center center no-repeat #fff;height:20px;width:20px;border-radius:100%;position:absolute;top:-3px;right:13px;background-size:70%;box-shadow:0 0 4px 0 black;}
.fcre-photo-gallery-metabox-list li:hover .change-photo{display:block;}
.fcre-photo-gallery-metabox-list li .remove-photo{display:none;background:url("../img/close.png") center center no-repeat #fff;height:20px;width:20px;border-radius:100%;position:absolute;top:-4px;right:-10px;background-size:70%;box-shadow:0 0 4px 0 black;}
.fcre-photo-gallery-metabox-list li:hover .remove-photo{display:block;}

 
.fcre-repeater-item {
    position: relative;
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    background: #fafafa;
}

.fcre-repeater-drag {
    position: absolute;
    left: -25px;
    top: 10px;
    cursor: move;
    font-size: 18px;
    color: #999;
}

.fcre-sortable-placeholder {
    background: #f0f0f0;
    border: 2px dashed #ccc;
    height: 50px;
    margin-bottom: 10px;
}

.fcre-repeater-field {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}
.fcre-repeater-field label {
    width: 120px;
}




.fcre-column {
    flex: 1;
    width: 45%;
    background: #fff;
    padding: 10px;
    border: 1px solid #ddd;
    min-height: 350px;
}

.fcre-list {
    list-style: none;
    margin: 10px 0 0 0;
    padding: 0;
    min-height: 200px;
    border: 1px solid #ccc;
}

.fcre-item {
    padding: 8px 10px;
    background: #f9f9f9;
    margin-bottom: 6px;
    border: 1px solid #ccc;
    cursor: move;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fcre-item.fcre-disabled {
    background: #eee;
    color: gray;
    cursor: not-allowed;
    pointer-events: none;
}

.fcre-remove {
    cursor: pointer;
    color: red;
    margin-left: 10px;
}

.fcre-search {
    width: 100%;
    padding: 6px 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
}

.fcre-placeholder {
    background: #f0f0f0;
    border: 1px dashed #ccc;
}


.fcre-file-upload-wrapper {
    margin-bottom: 15px;
}

.fcre-file-uploader {
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    position: relative;
}

.fcre-file-uploader.has-file {
    padding: 15px;
}

.fcre-file-uploader.no-file {
    padding: 0;
}

/* Upload Area Styles */
.fcre-file-upload-area {
    padding: 40px 20px;
    text-align: center;
    border: 2px dashed #ddd;
    border-radius: 4px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.fcre-file-upload-area:hover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.fcre-upload-placeholder .dashicons {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 10px;
    display: block;
}

.fcre-upload-placeholder p {
    margin: 10px 0;
    color: #666;
    font-size: 14px;
}

/* File Preview Styles */
.fcre-file-preview-container {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.fcre-file-icon-wrapper {
    flex-shrink: 0;
}

.fcre-file-link {
    display: block;
    width: fit-content;
    text-decoration: none;
    padding: 8px 0 8px 35px;
    font-size: 14px;
    font-weight: 500;
    color: #0073aa;
    background-size: 24px 24px;
    background-repeat: no-repeat;
    background-position: left center;
}

.fcre-file-link:hover {
    color: #005a87;
}

/* File type icons */
.fcre-file-link.pdf {
    background-image: url('../img/pdf.png');
}

.fcre-file-link.doc,
.fcre-file-link.docx {
    background-image: url('../img/word.png');
}

.fcre-file-link.xls,
.fcre-file-link.xlsx {
    background-image: url('../img/excel.png');
}

.fcre-file-link.ppt,
.fcre-file-link.pptx {
    background-image: url('../img/pptx.png');
}

.fcre-file-link.zip,
.fcre-file-link.rar {
    background-image: url('../img/zip.png');
}

.fcre-file-link.jpg,
.fcre-file-link.jpeg,
.fcre-file-link.png,
.fcre-file-link.gif,
.fcre-file-link.bmp,
.fcre-file-link.webp,
.fcre-file-link.svg {
    background-image: url('../img/image.png');
}

.fcre-file-details {
    flex-grow: 1;
}

.fcre-file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    word-break: break-word;
}

.fcre-file-size {
    font-size: 12px;
    color: #666;
}

.fcre-file-actions {
    display: flex;
    gap: 5px;
    flex-shrink: 0;
}

.fcre-file-actions .button {
    padding: 6px 8px;
    min-height: auto;
    line-height: 1;
}

.fcre-file-actions .dashicons {
    font-size: 16px;
    line-height: 1;
}

.fcre-change-file .dashicons {
    color: #0073aa;
}

.fcre-remove-file .dashicons {
    color: #d63638;
}

.fcre-change-file:hover .dashicons {
    color: #005a87;
}

.fcre-remove-file:hover .dashicons {
    color: #b32d2e;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fcre-file-preview-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .fcre-file-actions {
        align-self: flex-end;
    }

    .fcre-upload-placeholder {
        padding: 30px 15px;
    }

    .fcre-upload-placeholder .dashicons {
        font-size: 36px;
    }
}

/* Drag and drop styling */
.fcre-file-upload-area.dragover {
    border-color: #0073aa;
    background: #e6f3ff;
    transform: scale(1.02);
}

/* Loading state */
.fcre-file-uploader.loading {
    opacity: 0.7;
    pointer-events: none;
}

.fcre-file-uploader.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #0073aa;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error state */
.fcre-file-uploader.error {
    border-color: #d63638;
}

.fcre-file-uploader.error .fcre-file-upload-area {
    border-color: #d63638;
    background: #ffeaea;
}

/* Success state */
.fcre-file-uploader.success .fcre-file-preview-container {
    border-left: 4px solid #00a32a;
    padding-left: 15px;
}